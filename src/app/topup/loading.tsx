/**
 * Top-up Page Loading Component
 * 
 * Loading screen for the token top-up page
 */

"use client";

import React from "react";
import { motion } from "framer-motion";
import { Loader2, Coins, Wallet, CreditCard } from "lucide-react";

export default function TopupLoading() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-8">
      <div className="max-w-md w-full text-center space-y-8">
        {/* Animated Coins Icon */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6 }}
          className="relative mx-auto w-20 h-20"
        >
          <motion.div
            animate={{ 
              scale: [1, 1.1, 1],
              rotate: [0, 360]
            }}
            transition={{ 
              duration: 4, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
            className="absolute inset-0 rounded-full bg-gradient-to-br from-yellow-500/20 to-orange-500/20 flex items-center justify-center"
          >
            <Coins className="w-8 h-8 text-yellow-500" />
          </motion.div>
          
          {/* Floating coins effect */}
          {[0, 90, 180, 270].map((rotation, index) => (
            <motion.div
              key={index}
              animate={{ 
                rotate: 360,
                y: [0, -10, 0]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity, 
                ease: "easeInOut",
                delay: index * 0.2
              }}
              className="absolute inset-0"
              style={{ transform: `rotate(${rotation}deg)` }}
            >
              <div className="absolute -top-1 left-1/2 w-2 h-2 bg-yellow-500/60 rounded-full transform -translate-x-1/2" />
            </motion.div>
          ))}
        </motion.div>

        {/* Title and Description */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="space-y-4"
        >
          <h2 className="text-2xl font-bold bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent">
            Token Top-up
          </h2>
          <p className="text-muted-foreground">
            Loading your wallet and payment options
          </p>
        </motion.div>

        {/* Loading Steps */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="space-y-3"
        >
          {[
            { icon: Wallet, text: "Connecting to wallet", delay: 0 },
            { icon: Coins, text: "Loading token balance", delay: 0.3 },
            { icon: CreditCard, text: "Preparing payment methods", delay: 0.6 }
          ].map((item, index) => (
            <motion.div
              key={item.text}
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.8 + item.delay, duration: 0.4 }}
              className="flex items-center justify-center space-x-2 text-sm text-muted-foreground"
            >
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                  rotate: [0, 15, -15, 0]
                }}
                transition={{ 
                  duration: 2, 
                  repeat: Infinity, 
                  delay: item.delay 
                }}
              >
                <item.icon className="w-4 h-4" />
              </motion.div>
              <span>{item.text}</span>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ 
                  duration: 1, 
                  repeat: Infinity, 
                  ease: "linear",
                  delay: item.delay 
                }}
              >
                <Loader2 className="w-3 h-3" />
              </motion.div>
            </motion.div>
          ))}
        </motion.div>

        {/* Progress Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5, duration: 0.5 }}
          className="w-full h-1 bg-muted rounded-full overflow-hidden"
        >
          <motion.div
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{ 
              duration: 3, 
              ease: "easeInOut",
              repeat: Infinity,
              repeatType: "reverse"
            }}
            className="h-full bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full"
          />
        </motion.div>

        {/* Security Note */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 2, duration: 0.5 }}
          className="text-center p-3 rounded-lg bg-green-500/10 border border-green-500/20"
        >
          <p className="text-xs text-green-600 dark:text-green-400">
            🔒 Your wallet connection is secure and encrypted
          </p>
        </motion.div>
      </div>
    </div>
  );
}
