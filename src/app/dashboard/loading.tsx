/**
 * Dashboard Loading Component
 * 
 * Specific loading screen for the dashboard page with relevant animations
 * and information about what's being loaded.
 */

"use client";

import React from "react";
import { motion } from "framer-motion";
import { Loader2, Twitter, BarChart3, MessageSquare, Users } from "lucide-react";

export default function DashboardLoading() {
  const loadingSteps = [
    { icon: Twitter, label: "Loading Twitter accounts", delay: 0 },
    { icon: MessageSquare, label: "Fetching analyzed tweets", delay: 0.3 },
    { icon: BarChart3, label: "Preparing analytics", delay: 0.6 },
    { icon: Users, label: "Setting up dashboard", delay: 0.9 },
  ];

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-8">
      <div className="max-w-md w-full space-y-8">
        {/* Main Loading Animation */}
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center space-y-6"
        >
          {/* Spinning Dashboard Icon */}
          <div className="relative mx-auto w-16 h-16">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
              className="absolute inset-0 rounded-full border-4 border-primary/20 border-t-primary"
            />
            <div className="absolute inset-2 rounded-full bg-primary/10 flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-primary" />
            </div>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-foreground">
              Loading Dashboard
            </h2>
            <p className="text-sm text-muted-foreground">
              Preparing your Twitter analytics workspace
            </p>
          </div>
        </motion.div>

        {/* Loading Steps */}
        <div className="space-y-4">
          {loadingSteps.map((step, index) => (
            <motion.div
              key={step.label}
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: step.delay, duration: 0.5 }}
              className="flex items-center space-x-3 p-3 rounded-lg bg-muted/50"
            >
              <motion.div
                animate={{ 
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{ 
                  duration: 2, 
                  repeat: Infinity, 
                  delay: step.delay + 0.5 
                }}
                className="flex-shrink-0"
              >
                <step.icon className="w-5 h-5 text-primary" />
              </motion.div>
              
              <div className="flex-1 flex items-center justify-between">
                <span className="text-sm text-foreground">{step.label}</span>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ 
                    duration: 1, 
                    repeat: Infinity, 
                    ease: "linear",
                    delay: step.delay 
                  }}
                >
                  <Loader2 className="w-4 h-4 text-muted-foreground" />
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Progress Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          className="space-y-2"
        >
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Initializing...</span>
            <span>Please wait</span>
          </div>
          
          <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
            <motion.div
              initial={{ width: "0%" }}
              animate={{ width: "100%" }}
              transition={{ 
                duration: 4, 
                ease: "easeInOut",
                repeat: Infinity,
                repeatType: "reverse"
              }}
              className="h-full bg-gradient-to-r from-primary to-secondary rounded-full"
            />
          </div>
        </motion.div>

        {/* Helpful Tip */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5, duration: 0.5 }}
          className="text-center p-4 rounded-lg bg-primary/5 border border-primary/10"
        >
          <p className="text-xs text-muted-foreground">
            💡 <strong>Tip:</strong> Your dashboard loads faster on subsequent visits thanks to intelligent caching
          </p>
        </motion.div>
      </div>
    </div>
  );
}
