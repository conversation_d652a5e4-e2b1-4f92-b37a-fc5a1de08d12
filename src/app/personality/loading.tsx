/**
 * Personality Analysis Loading Component
 * 
 * Loading screen for the personality analysis page
 */

"use client";

import React from "react";
import { motion } from "framer-motion";
import { Loader2, <PERSON>, User, <PERSON><PERSON>hart3 } from "lucide-react";

export default function PersonalityLoading() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-8">
      <div className="max-w-md w-full text-center space-y-8">
        {/* Animated Brain Icon */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6 }}
          className="relative mx-auto w-20 h-20"
        >
          <motion.div
            animate={{ 
              scale: [1, 1.1, 1],
              rotate: [0, 5, -5, 0]
            }}
            transition={{ 
              duration: 3, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
            className="absolute inset-0 rounded-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center"
          >
            <Brain className="w-8 h-8 text-purple-500" />
          </motion.div>
          
          {/* Neural network effect */}
          {[0, 60, 120, 180, 240, 300].map((rotation, index) => (
            <motion.div
              key={index}
              animate={{ rotate: 360 }}
              transition={{ 
                duration: 5, 
                repeat: Infinity, 
                ease: "linear",
                delay: index * 0.2
              }}
              className="absolute inset-0"
              style={{ transform: `rotate(${rotation}deg)` }}
            >
              <div className="absolute -top-0.5 left-1/2 w-1 h-1 bg-purple-500/40 rounded-full transform -translate-x-1/2" />
            </motion.div>
          ))}
        </motion.div>

        {/* Title and Description */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="space-y-4"
        >
          <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-500 to-pink-500 bg-clip-text text-transparent">
            Personality Analysis
          </h2>
          <p className="text-muted-foreground">
            Preparing AI-powered personality insights
          </p>
        </motion.div>

        {/* Loading Steps */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="space-y-3"
        >
          {[
            { icon: User, text: "Loading user data", delay: 0 },
            { icon: Brain, text: "Initializing AI models", delay: 0.3 },
            { icon: BarChart3, text: "Preparing analysis tools", delay: 0.6 }
          ].map((item, index) => (
            <motion.div
              key={item.text}
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.8 + item.delay, duration: 0.4 }}
              className="flex items-center justify-center space-x-2 text-sm text-muted-foreground"
            >
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                  rotate: [0, 10, -10, 0]
                }}
                transition={{ 
                  duration: 2, 
                  repeat: Infinity, 
                  delay: item.delay 
                }}
              >
                <item.icon className="w-4 h-4" />
              </motion.div>
              <span>{item.text}</span>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ 
                  duration: 1, 
                  repeat: Infinity, 
                  ease: "linear",
                  delay: item.delay 
                }}
              >
                <Loader2 className="w-3 h-3" />
              </motion.div>
            </motion.div>
          ))}
        </motion.div>

        {/* Progress Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5, duration: 0.5 }}
          className="w-full h-1 bg-muted rounded-full overflow-hidden"
        >
          <motion.div
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{ 
              duration: 3, 
              ease: "easeInOut",
              repeat: Infinity,
              repeatType: "reverse"
            }}
            className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
          />
        </motion.div>
      </div>
    </div>
  );
}
