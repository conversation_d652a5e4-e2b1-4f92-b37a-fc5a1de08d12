/**
 * Search Page Loading Component
 * 
 * Loading screen for the search/AI agent page
 */

"use client";

import React from "react";
import { motion } from "framer-motion";
import { Loader2, Search, Brain, Globe } from "lucide-react";

export default function SearchLoading() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-8">
      <div className="max-w-md w-full text-center space-y-8">
        {/* Animated Search Icon */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6 }}
          className="relative mx-auto w-20 h-20"
        >
          <motion.div
            animate={{ 
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360]
            }}
            transition={{ 
              duration: 4, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
            className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center"
          >
            <Search className="w-8 h-8 text-blue-500" />
          </motion.div>
          
          {/* Pulsing ring */}
          <motion.div
            animate={{ 
              scale: [1, 1.5, 1],
              opacity: [0.5, 0, 0.5]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
            className="absolute inset-0 rounded-full border-2 border-blue-500/30"
          />
        </motion.div>

        {/* Title and Description */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="space-y-4"
        >
          <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">
            AI Search Agent
          </h2>
          <p className="text-muted-foreground">
            Initializing intelligent search capabilities
          </p>
        </motion.div>

        {/* Loading Steps */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="space-y-3"
        >
          {[
            { icon: Brain, text: "Loading AI models", delay: 0 },
            { icon: Globe, text: "Connecting to search APIs", delay: 0.3 },
            { icon: Search, text: "Preparing search interface", delay: 0.6 }
          ].map((item, index) => (
            <motion.div
              key={item.text}
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.8 + item.delay, duration: 0.4 }}
              className="flex items-center justify-center space-x-2 text-sm text-muted-foreground"
            >
              <motion.div
                animate={{ 
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{ 
                  duration: 2, 
                  repeat: Infinity, 
                  delay: item.delay 
                }}
              >
                <item.icon className="w-4 h-4" />
              </motion.div>
              <span>{item.text}</span>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ 
                  duration: 1, 
                  repeat: Infinity, 
                  ease: "linear",
                  delay: item.delay 
                }}
              >
                <Loader2 className="w-3 h-3" />
              </motion.div>
            </motion.div>
          ))}
        </motion.div>

        {/* Progress Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5, duration: 0.5 }}
          className="w-full h-1 bg-muted rounded-full overflow-hidden"
        >
          <motion.div
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{ 
              duration: 3, 
              ease: "easeInOut",
              repeat: Infinity,
              repeatType: "reverse"
            }}
            className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
          />
        </motion.div>
      </div>
    </div>
  );
}
